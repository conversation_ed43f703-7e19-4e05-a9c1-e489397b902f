#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Electron development environment...');

// Check if Docker is running
function checkDockerRunning() {
  return new Promise((resolve) => {
    exec('docker info', (error) => {
      resolve(!error);
    });
  });
}

// Check if database containers are running
function checkDatabaseRunning() {
  return new Promise((resolve) => {
    exec('docker compose --file .base/docker-compose.yml ps --services --filter "status=running"', (error, stdout) => {
      if (error) {
        resolve(false);
      } else {
        const runningServices = stdout.trim().split('\n').filter(s => s);
        resolve(runningServices.includes('postgres'));
      }
    });
  });
}

// Start database containers
function startDatabase() {
  return new Promise((resolve, reject) => {
    console.log('🗄️  Starting database containers...');
    const dbProcess = spawn('docker', ['compose', '--file', '.base/docker-compose.yml', 'up', '-d'], {
      stdio: 'inherit'
    });

    dbProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Database containers started successfully');
        // Wait a moment for PostgreSQL to be ready
        setTimeout(resolve, 5000);
      } else {
        reject(new Error(`Database startup failed with code ${code}`));
      }
    });

    dbProcess.on('error', reject);
  });
}

// Main startup function
async function startDevelopmentEnvironment() {
  try {
    // Check Docker status
    const dockerRunning = await checkDockerRunning();
    if (!dockerRunning) {
      console.error('❌ Docker is not running. Please start Docker Desktop and try again.');
      console.log('💡 On macOS: Open Docker Desktop application');
      console.log('💡 On Windows: Start Docker Desktop');
      console.log('💡 On Linux: sudo systemctl start docker');
      process.exit(1);
    }

    console.log('✅ Docker is running');

    // Check if database is running
    const dbRunning = await checkDatabaseRunning();
    if (!dbRunning) {
      await startDatabase();
    } else {
      console.log('✅ Database containers are already running');
    }

    // Start the backend server
    console.log('🔧 Starting backend server...');
    const backendProcess = spawn('bun', ['run', 'base/cmd.ts', 'server'], {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development',
        SERVER_PORT: '3000',
        SERVER_HOST: 'localhost'
      }
    });

    // Wait a moment for the backend to start
    setTimeout(() => {
      console.log('⚡ Starting Electron...');

      // Start Electron
      const electronProcess = spawn('electron', [path.join(__dirname, '../electron/main.js')], {
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_ENV: 'development',
          BACKEND_PORT: '3000',
          BACKEND_HOST: 'localhost'
        }
      });

      electronProcess.on('close', () => {
        console.log('🛑 Electron closed, shutting down backend...');
        backendProcess.kill();
        process.exit(0);
      });

      electronProcess.on('error', (error) => {
        console.error('❌ Electron error:', error);
        backendProcess.kill();
        process.exit(1);
      });

    }, 5000); // Increased wait time for database connection

    backendProcess.on('error', (error) => {
      console.error('❌ Backend server error:', error);
      process.exit(1);
    });

    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down development environment...');
      backendProcess.kill();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 Shutting down development environment...');
      backendProcess.kill();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Failed to start development environment:', error.message);
    process.exit(1);
  }
}

// Start the development environment
startDevelopmentEnvironment();
