#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');

console.log('🗄️  Database Setup Script');
console.log('========================\n');

// Check if Docker is running
function checkDockerRunning() {
  return new Promise((resolve) => {
    exec('docker info', (error) => {
      resolve(!error);
    });
  });
}

// Check if .env file exists
function checkEnvFile() {
  return fs.existsSync('.env');
}

// Start database containers
function startDatabase() {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting PostgreSQL and ElectricSQL containers...');
    const dbProcess = spawn('docker', ['compose', '--file', '.base/docker-compose.yml', 'up', '-d'], {
      stdio: 'inherit'
    });
    
    dbProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Database containers started successfully');
        resolve();
      } else {
        reject(new Error(`Database startup failed with code ${code}`));
      }
    });
    
    dbProcess.on('error', reject);
  });
}

// Wait for database to be ready
function waitForDatabase() {
  return new Promise((resolve) => {
    console.log('⏳ Waiting for PostgreSQL to be ready...');
    setTimeout(() => {
      console.log('✅ Database should be ready now');
      resolve();
    }, 10000);
  });
}

// Run database migrations
function runMigrations() {
  return new Promise((resolve, reject) => {
    console.log('🔄 Running database migrations...');
    const migrateProcess = spawn('bun', ['drizzle-kit', '--config', './base/backend/db/drizzle.config.ts', 'push'], {
      stdio: 'inherit'
    });
    
    migrateProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Database migrations completed');
        resolve();
      } else {
        console.log('⚠️  Migration failed, but continuing...');
        resolve(); // Don't fail the setup if migrations fail
      }
    });
    
    migrateProcess.on('error', (error) => {
      console.log('⚠️  Migration error:', error.message);
      resolve(); // Don't fail the setup if migrations fail
    });
  });
}

// Test database connection
function testConnection() {
  return new Promise((resolve, reject) => {
    console.log('🔍 Testing database connection...');
    const testProcess = spawn('bun', ['run', 'base/cmd.ts', 'db', 'reset'], {
      stdio: 'pipe'
    });
    
    let output = '';
    testProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    testProcess.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Database connection test successful');
        resolve();
      } else {
        console.log('❌ Database connection test failed');
        console.log('Output:', output);
        reject(new Error('Database connection test failed'));
      }
    });
    
    testProcess.on('error', reject);
  });
}

// Main setup function
async function setupDatabase() {
  try {
    // Check prerequisites
    console.log('1️⃣  Checking prerequisites...');
    
    if (!checkEnvFile()) {
      console.error('❌ .env file not found. Please create it with database configuration.');
      process.exit(1);
    }
    console.log('✅ .env file found');
    
    const dockerRunning = await checkDockerRunning();
    if (!dockerRunning) {
      console.error('❌ Docker is not running. Please start Docker and try again.');
      console.log('\n💡 How to start Docker:');
      console.log('   macOS: Open Docker Desktop application');
      console.log('   Windows: Start Docker Desktop');
      console.log('   Linux: sudo systemctl start docker');
      process.exit(1);
    }
    console.log('✅ Docker is running');
    
    // Start database
    console.log('\n2️⃣  Starting database containers...');
    await startDatabase();
    
    // Wait for database to be ready
    console.log('\n3️⃣  Waiting for database to be ready...');
    await waitForDatabase();
    
    // Run migrations
    console.log('\n4️⃣  Setting up database schema...');
    await runMigrations();
    
    // Test connection
    console.log('\n5️⃣  Testing database connection...');
    await testConnection();
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   • Run: npm run electron:dev');
    console.log('   • Or: npm run dev (for web only)');
    console.log('\n📊 Database info:');
    console.log('   • Host: localhost:54321');
    console.log('   • Database: postgres');
    console.log('   • User: postgres');
    console.log('   • Password: password');
    
  } catch (error) {
    console.error('\n❌ Database setup failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure Docker is running');
    console.log('   • Check if port 54321 is available');
    console.log('   • Verify .env file configuration');
    console.log('   • Try: docker compose --file .base/docker-compose.yml down');
    console.log('   • Then run this script again');
    process.exit(1);
  }
}

// Handle script arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Database Setup Script');
  console.log('====================');
  console.log('');
  console.log('This script sets up the PostgreSQL database for tini-melon.');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/setup-database.js');
  console.log('');
  console.log('What it does:');
  console.log('  1. Checks if Docker is running');
  console.log('  2. Starts PostgreSQL and ElectricSQL containers');
  console.log('  3. Waits for database to be ready');
  console.log('  4. Runs database migrations');
  console.log('  5. Tests the database connection');
  console.log('');
  console.log('Requirements:');
  console.log('  • Docker Desktop running');
  console.log('  • .env file with database configuration');
  console.log('  • Port 54321 available');
  process.exit(0);
}

// Run the setup
setupDatabase();
