import { derive, provide } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { getServerConfig } from "@/base/backend/config";
import { rpc } from "@/base/backend/routes";
import { serve } from "bun";

import { shape } from "@/mod/backend/shape";
import frontend from "@/mod/frontend/index.html";

const serverConfig = provide(() => getServerConfig());
export default derive(
  [serverConfig, logger('server'), rpc, shape],
  async ([config, sLogger, rpc, shape], ctl) => {
    sLogger.info(`Starting server on ${config.host}:${config.port}...`);

    const server = serve({
      port: config.port,
      hostname: config.host,
      development: true,
      routes: {
        "/": frontend,
        "/rpc": rpc,
        "/shape": shape
      }
    })

    ctl.cleanup(async () => {
      sLogger.info("Shutting down server...");
      await server.stop();
    })

    await new Promise<void>(() => {})
  }
)