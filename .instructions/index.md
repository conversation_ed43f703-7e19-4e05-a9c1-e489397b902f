# Scaf Codebase Documentation

This documentation set provides an AI-friendly overview of the Scaf codebase, focusing on its unique architecture and patterns.

## Documentation Files

1. [**Pumped-fn Pattern**](pumped-fn-pattern.md) - Core pattern for state management and dependency injection
2. [**Pumped-fn Features**](pumped-fn-features.md) - Detailed examples of pumped-fn in frontend and backend
3. [**State Definition**](state-definition.md) - How state is defined using pumped-fn
4. [**Component Integration**](component-integration.md) - How React components consume pumped-fn state
5. [**Base/Mod Architecture**](base-mod-architecture.md) - How the codebase is organized into base and mod directories
6. [**Data Flow**](data-flow.md) - How data flows through the application
7. [**Styling**](styling.md) - Tailwind and DaisyUI usage

## Key Concepts

- **Pumped-fn Pattern**: The codebase uses `@pumped-fn/core-next` for state management and dependency injection
- **Reactive Components**: React components use `@pumped-fn/react` to subscribe to state changes
- **Base/Mod Architecture**: Clear separation between reusable infrastructure (base) and application-specific code (mod)

## Technology Stack

- **Runtime**: Bun
- **Frontend**: React, Tailwind CSS, DaisyUI
- **State Management**: pumped-fn
- **Backend**: Bun server
- **Database**: PostgreSQL with Drizzle ORM

## Repository Structure

```
/
├── base/           # Reusable infrastructure
│   ├── backend/    # Backend infrastructure
│   ├── cmds/       # Base commands
│   ├── db/         # Database infrastructure
│   ├── dual/       # Shared code (frontend/backend)
│   └── utils/      # Utility functions
├── mod/            # Application-specific code
│   ├── backend/    # Backend implementation
│   ├── cmds/       # Application commands
│   ├── dual/       # Shared types and RPC definitions
│   └── frontend/   # Frontend implementation
│       ├── components/  # React components
│       └── pumped.*.ts  # pumped-fn state definitions
```

Start with [Pumped-fn Pattern](pumped-fn-pattern.md) to understand the core pattern used throughout the codebase.

