# Data Flow

This document describes how data flows through the Scaf application, from database to frontend and back.

## Overview

The data flow in Scaf follows this pattern:

1. **Database Schema**: Defines the structure of the data
2. **Database Connection**: Connects to the database using pumped-fn
3. **Backend Routes**: Handle API requests
4. **Frontend State**: Manages state using pumped-fn
5. **React Components**: Display and interact with the state

## Database to Frontend Flow

### 1. Database Schema

```typescript
// mod/backend/schema.ts
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  email: text("email").unique(),
  name: text("name"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const todos = pgTable("todos", {
  id: serial("id").primaryKey(),
  userId: serial("user_id"),
  title: text("title"),
  description: text("description"),
  completed: boolean("completed").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});
```

### 2. Database Connection

```typescript
// base/db/index.ts
export const connection = derive([config, logger('db')], async ([config, logger]) => {
  // Database connection setup
  return db;
});
```

### 3. Backend Routes

```typescript
// mod/backend/routes.ts
export const getTodosRoute = derive(
  [connection, logger('route.getTodos')],
  async ([db, logger]) => async ({ input }) => {
    const { userId } = input;
    return await db.select().from(todosTable).where(eq(todosTable.userId, userId));
  }
);
```

### 4. Frontend State

```typescript
// mod/frontend/pumped.todo.ts
export const users = provide(() => mockUsers);
export const todos = provide(() => mockTodos);

export const selectedUser = derive([users.reactive, selectedUserId.reactive], 
  ([users, userId]) => users.find(user => user.id === userId) || null
);

export const userTodos = derive(
  [selectedUser.reactive, todos.reactive],
  ([user, todos]) => todos.filter(todo => todo.userId === user?.id)
);
```

### 5. React Components

```typescript
// mod/frontend/components/TodoList.tsx
const TodoList = () => {
  const [selectedUser, userTodos] = useResolves(
    app.selectedUser.reactive, 
    app.userTodos.reactive
  );
  
  return (
    <div>
      <h2>Tasks for {selectedUser?.name}</h2>
      <ul>
        {userTodos.map(todo => (
          <li key={todo.id}>{todo.title}</li>
        ))}
      </ul>
    </div>
  );
};
```

## Frontend to Database Flow

### 1. User Interaction

```typescript
// mod/frontend/components/AddTodoForm.tsx
const handleSubmit = (e) => {
  e.preventDefault();
  todosCtl.addTodo({
    userId: selectedUser.id,
    title,
    description,
    completed: false,
  });
};
```

### 2. State Update

```typescript
// mod/frontend/pumped.todo.ts
export const todosCtl = derive(
  [todos.static],
  ([todosCtl]) => ({
    addTodo: (todoData) => {
      todosCtl.update(todos => [
        ...todos,
        {
          ...todoData,
          id: Date.now(),
          createdAt: new Date(),
        }
      ]);
    }
  })
);
```

### 3. API Call

```typescript
// mod/frontend/client.ts
export const client = derive(
  [sender, logger('client')],
  ([sender, logger]) => ({
    createTodo: async (todo) => {
      const response = await sender('/api/todos', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(todo)
      });
      return response.json();
    }
  })
);
```

### 4. Backend Route

```typescript
// mod/backend/routes.ts
export const createTodoRoute = derive(
  [connection, logger('route.createTodo')],
  async ([db, logger]) => async ({ input }) => {
    const todo = await db.insert(todos).values(input).returning();
    return todo[0];
  }
);
```

## Complete Flow Example

1. **User adds a todo**:
   - User enters todo title and description
   - User clicks "Add Todo" button
   - `todosCtl.addTodo` function is called
   - State is updated
   - UI is updated automatically due to reactive state
   - API call is made to save the todo
   - Server validates the input
   - Server inserts the todo into the database
   - Server returns the new todo
   - Frontend updates the state with the returned todo

## Key Benefits

1. **Reactive Updates**: UI automatically updates when state changes
2. **Type Safety**: TypeScript ensures type safety throughout the application
3. **Separation of Concerns**: State management is separated from UI components
4. **Testability**: Each layer can be tested in isolation

