# Extension Patterns

The Scaf codebase uses a unique base/mod architecture that enables extension and customization without modifying base code.

## Core Principles

1. **Base**: Provides reusable infrastructure, utilities, and patterns
2. **Mod**: Contains application-specific implementations and customizations
3. **Extension over Modification**: Base components are designed to be extended rather than modified

## Extension Mechanisms

### 1. Import Redirection

Base components often import from mod to allow customization:

```typescript
// base/db/schema.ts
export * from '@/mod/backend/schema'
```

This pattern allows mod to define the schema while base provides the infrastructure for using that schema.

### 2. Command Resolution Order

The command system prioritizes mod commands over base commands:

```typescript
// base/cmd.ts
const cmd = await Promise.allSettled([
    import(`@/mod/cmds/${firstCmd}`),
    import(`@/base/cmds/${firstCmd}`)
  ])
  .then(results => results.find(r => r.status === "fulfilled")?.value)
```

This allows mod to override base commands by providing an implementation with the same name.

### 3. Dependency Injection

The pumped-fn pattern enables mod components to use base infrastructure while providing their own implementations:

```typescript
// mod/backend/routes.ts
export const createUserRoute = router.implements(
  'createUser',
  derive(
    [connection, logger('route.createUser')],
    async ([db, logger]) => async ({ input }) => {
      // Implementation using base-provided dependencies
    }
  )
)
```

### 4. Service Implementation

The RPC system defines service interfaces in mod and implements them using base infrastructure:

```typescript
// mod/dual/rpc.ts
export const userEndpoint = define.service({
  createUser: {
    input: schema.UserInsertSchema,
    output: z.void(),
  }
})

// mod/backend/routes.ts
export const createUserRoute = router.implements(
  'createUser',
  derive(
    [connection, logger('route.createUser')],
    async ([db, logger]) => async ({ input }) => {
      // Implementation
    }
  )
)
```

## Directory Responsibilities

### Base Directory

```
base/
├── backend/       # Backend infrastructure
│   ├── config.ts  # Configuration definitions
│   └── routes.ts  # Base route handling
├── cmd.ts         # Command runner entry point
├── cmds/          # Base commands
│   └── server.ts  # Server command
├── db/            # Database infrastructure
│   ├── index.ts   # Database connection
│   └── schema.ts  # Schema (imports from mod)
├── dual/          # Shared code
│   └── logger.ts  # Logging utility
└── utils/         # Utility functions
    └── shell.ts   # Shell command utilities
```

Responsibilities:
- Infrastructure components
- Utility functions
- Extension points
- Command runner
- Base implementations

### Mod Directory

```
mod/
├── backend/       # Backend implementation
│   ├── routes.ts  # Route implementations
│   ├── schema.ts  # Database schema
│   └── shapes.ts  # Shape definitions
├── cmds/          # Application commands
│   └── db.ts      # Database commands
├── dual/          # Shared types and definitions
│   ├── rpc.ts     # RPC endpoint definitions
│   └── types.ts   # Type definitions
└── frontend/      # Frontend implementation
    ├── app.tsx    # Main application component
    ├── components/# React components
    └── pumped.*.ts# State definitions
```

Responsibilities:
- Application-specific implementations
- Database schema
- RPC endpoint definitions
- Frontend components
- State management

## Extension Examples

### 1. Adding a New Command

To add a new command:

1. Create a file in `mod/cmds/` with the command name:

```typescript
// mod/cmds/new-command.ts
import { derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";

export default derive(
  [logger('new-command')],
  async ([logger], ctl) => {
    logger.info("Running new command...");
    // Implementation
  }
);
```

2. Run the command:

```bash
bun run script new-command
```

The command runner will automatically find and execute the command.

### 2. Adding a New Database Table

To add a new database table:

1. Add the table definition to `mod/backend/schema.ts`:

```typescript
export const newTable = pgTable("new_table", {
  id: serial("id").primaryKey().notNull(),
  name: text("name").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});
```

2. Add relations if needed:

```typescript
export const newTableRelations = relations(newTable, ({ one }) => ({
  user: one(users, {
    fields: [newTable.userId],
    references: [users.id],
  }),
}));
```

The base infrastructure will automatically use the new table.

### 3. Adding a New RPC Endpoint

To add a new RPC endpoint:

1. Add the endpoint definition to `mod/dual/rpc.ts`:

```typescript
export const newEndpoint = define.service({
  newOperation: {
    input: z.object({
      name: z.string(),
    }),
    output: z.void(),
  },
});
```

2. Add the implementation to `mod/backend/routes.ts`:

```typescript
export const newOperationRoute = router.implements(
  'newOperation',
  derive(
    [connection, logger('route.newOperation')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Performing new operation with input:', input);
      // Implementation
    }
  )
);
```

The base RPC system will automatically use the new endpoint.

### 4. Adding a New Frontend Feature

To add a new frontend feature:

1. Add state definitions to `mod/frontend/pumped.todo.ts`:

```typescript
export const newFeatureState = provide(() => initialState);
export const newFeatureComputed = derive(
  [newFeatureState.reactive, otherState.reactive],
  ([state, otherState]) => {
    // Compute derived state
    return derivedState;
  }
);
```

2. Add a new component to `mod/frontend/components/`:

```typescript
import { useResolves } from '@pumped-fn/react';
import { app } from '../pumped.todo';

const NewFeatureComponent: React.FC = () => {
  const [state, computedState] = useResolves(
    app.newFeatureState.reactive,
    app.newFeatureComputed.reactive
  );
  
  // Component implementation
};
```

3. Add the component to the application:

```typescript
import NewFeatureComponent from './components/NewFeatureComponent';

const App: React.FC = () => {
  return (
    <div>
      {/* Existing components */}
      <NewFeatureComponent />
    </div>
  );
};
```

## Benefits of the Base/Mod Pattern

1. **Reusability**: Base components can be reused across multiple applications
2. **Maintainability**: Clear separation of concerns
3. **Flexibility**: Mod can override or extend base behavior
4. **Testability**: Components can be tested in isolation

This pattern creates a flexible, maintainable architecture that enables rapid development while maintaining a solid foundation.

