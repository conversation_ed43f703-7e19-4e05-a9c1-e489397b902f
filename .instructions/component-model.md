# Component Model

The Scaf application uses a unique component model that integrates React with pumped-fn for reactive state management.

## Core Concepts

### 1. State Definition

State is defined using pumped-fn's `provide` and `derive` functions:

```typescript
// mod/frontend/pumped.todo.ts
export const users = provide(() => mockUsers);
export const todos = provide(() => mockTodos);

export const selectedUserId = provide(() => undefined as number | undefined);
export const selectedUser = derive([users.reactive, selectedUserId.reactive], ([users, userId]) => {
  if (userId === undefined) return null;
  return users.find(user => user.id === userId) || null;
});

export const userTodos = derive(
  [selectedUser.reactive, todos.reactive],
  ([user, todos]) => {
    if (!user) return [];
    return todos.filter(todo => todo.userId === user.id);
  }
);
```

### 2. State Access in Components

Components access state using `Resolves` component or `useResolves` hook:

```typescript
// Using Resolves component
const UserSelector: React.FC = () => <Resolves e={[users.reactive, selectedUser.reactive, setSelectedUser]}>
  {([users, selectedUser, setSelectedUser]) => (
    // Component implementation using resolved values
  )}
</Resolves>;

// Using useResolves hook
const TodoList: React.FC = () => {
  const [selectedUser, userTodos] = useResolves(app.selectedUser.reactive, app.userTodos.reactive);
  
  // Component implementation using resolved values
};
```

### 3. Component Composition

Components are composed to create the application:

```typescript
// mod/frontend/components/TodoApp.tsx
const TodoApp: React.FC = () => {
  return (
    <div className="min-h-screen bg-base-200 transition-colors duration-300">
      <Header />

      <main className="container mx-auto px-4 py-6 max-w-5xl">
        <HeroSection />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1">
            <div className="sticky top-4 space-y-6">
              <Suspense>
                <UserSelector />
                <AddTodoForm />
              </Suspense>
            </div>
          </div>

          <div className="lg:col-span-2">
            <TodoList />
          </div>
        </div>
      </main>

      <footer className="footer footer-center p-4 bg-base-300 text-base-content mt-12">
        <div>
          <p className="text-sm opacity-70">
            TaskBuddy &copy; {new Date().getFullYear()} - A clean, minimalist todo app
          </p>
        </div>
      </footer>
    </div>
  );
};
```

## Component Examples

### UserSelector Component

```typescript
// mod/frontend/components/UserSelector.tsx
import React from 'react';
import { Resolves } from '@pumped-fn/react';
import { users, setSelectedUser, selectedUser } from '../pumped.todo';

const UserSelector: React.FC = () => <Resolves e={[users.reactive, selectedUser.reactive, setSelectedUser]}>
  {([users, selectedUser, setSelectedUser]) => (
    <div className="mb-6">
      <div className="card bg-base-100 shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className="card-body p-4">
          <h2 className="card-title text-lg font-medium mb-2 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Select User
          </h2>
          
          <div className="form-control w-full">
            <select 
              className="select select-bordered w-full focus:ring-2 focus:ring-primary/50"
              value={selectedUser?.id || ''}
              onChange={(e) => setSelectedUser(Number(e.target.value))}
              aria-label="Select a user"
            >
              <option value="" disabled>Choose a user</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
          </div>
          
          {selectedUser && (
            <div className="mt-3 text-sm text-base-content/70">
              <p>{selectedUser.email}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )}
</Resolves>;

export default UserSelector;
```

### TodoList Component

```typescript
// mod/frontend/components/TodoList.tsx
import React, { useState } from 'react';
import TodoItem from './TodoItem';
import { app } from '../pumped.todo';
import { useResolves } from '@pumped-fn/react';

const TodoList: React.FC = () => {
  const [selectedUser, userTodos] = useResolves(app.selectedUser.reactive, app.userTodos.reactive);
  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');
  
  // Filter todos based on selected filter
  const filteredTodos = userTodos.filter(todo => {
    if (filter === 'active') return !todo.completed;
    if (filter === 'completed') return todo.completed;
    return true;
  });
  
  // Calculate counts
  const completedCount = userTodos.filter(todo => todo.completed).length;
  const activeCount = userTodos.length - completedCount;
  
  // Render empty state if no user selected
  if (!selectedUser) {
    return (
      <div className="card bg-base-100 shadow-sm p-8 text-center">
        <div className="flex flex-col items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <p className="text-base-content/50 mt-4 text-lg">Please select a user to view their tasks</p>
        </div>
      </div>
    );
  }
  
  // Render empty state if no todos
  if (userTodos.length === 0) {
    return (
      <div className="card bg-base-100 shadow-sm p-8 text-center">
        <div className="flex flex-col items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <p className="text-base-content/50 mt-4 text-lg">No tasks found for {selectedUser.name}</p>
          <p className="text-base-content/40 mt-2">Add a new task to get started</p>
        </div>
      </div>
    );
  }
  
  // Render todos
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Tasks
        </h2>
        
        <div className="tabs tabs-boxed">
          <button 
            className={`tab ${filter === 'all' ? 'tab-active' : ''}`}
            onClick={() => setFilter('all')}
            aria-label="Show all tasks"
          >
            All ({userTodos.length})
          </button>
          <button 
            className={`tab ${filter === 'active' ? 'tab-active' : ''}`}
            onClick={() => setFilter('active')}
            aria-label="Show active tasks"
          >
            Active ({activeCount})
          </button>
          <button 
            className={`tab ${filter === 'completed' ? 'tab-active' : ''}`}
            onClick={() => setFilter('completed')}
            aria-label="Show completed tasks"
          >
            Completed ({completedCount})
          </button>
        </div>
      </div>
      
      <div className="space-y-4">
        {filteredTodos.length > 0 ? (
          filteredTodos.map(todo => (
            <TodoItem key={todo.id} todo={todo} />
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-base-content/50">No {filter} tasks found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TodoList;
```

## Theme System

The application includes a theme system built with pumped-fn:

```typescript
// mod/frontend/pumped.theme.ts
import { provide, derive } from "@pumped-fn/core-next";

type Theme = 'light' | 'dark';

export const currentTheme = provide(() => {
  const storedTheme = window.localStorage.getItem('theme') as Theme | null;
  if (storedTheme) {
    return storedTheme;
  }

  const userPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  return userPrefersDark ? 'dark' : 'light';
});

export const switchTheme = derive([currentTheme.static], ([currentThemeCtl]) => {
  return () => {
    const newTheme = currentThemeCtl.get() === 'light' ? 'dark' : 'light';
    currentThemeCtl.update(newTheme);
    window.localStorage.setItem('theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };
});

export const theme = {
  currentTheme,
  switchTheme,
};
```

Components can use this theme system with the `useTheme` hook:

```typescript
import { useTheme } from '@pumped-fn/react';
import { theme } from '../pumped.theme';

function ThemedComponent() {
  const [currentTheme, switchTheme] = useTheme(theme);
  
  return (
    <div className={`min-h-screen ${currentTheme.background}`}>
      <button onClick={switchTheme}>Toggle Theme</button>
      {/* Component content */}
    </div>
  );
}
```

## Key Benefits

1. **Automatic Reactivity**: Components automatically update when state changes
2. **Clean Component Logic**: Components focus on rendering, with state management handled by pumped-fn
3. **Type Safety**: TypeScript ensures type safety throughout the component tree
4. **Testability**: Components can be tested in isolation with mocked state
5. **Performance**: Only components that depend on changed state are re-rendered

This component model creates a clean, reactive UI that is easy to reason about and maintain.

