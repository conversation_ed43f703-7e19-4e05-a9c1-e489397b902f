{"name": "tini-melon", "version": "1.0.0", "description": "A modern todo and user management application", "main": "electron/main.js", "homepage": "./", "author": "<PERSON>i <PERSON> Team", "license": "MIT", "dependencies": {"@electric-sql/client": "^1.0.4", "@go-task/cli": "^3.43.3", "@pumped-fn/core-next": "^0.5.41", "@pumped-fn/extra": "^0.5.20", "@pumped-fn/react": "^0.5.16", "@types/pg": "^8.15.2", "@types/react": "^19.1.6", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.5", "bun-plugin-tailwind": "^0.0.15", "consola": "^3.4.2", "daisyui": "^5.0.38", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.0", "drizzle-seed": "^0.3.1", "glob": "^11.0.2", "pg": "^8.16.0", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0", "tailwindcss": "^4.1.7", "ts-morph": "^26.0.0", "ts-to-zod": "^3.15.0", "typescript": "^5.8.3", "zod": "^3.25.34"}, "scripts": {"script": "bun run --bun base/cmd", "dev": "bun run --bun base/cmd server", "electron:dev": "node scripts/dev-electron.js", "electron:build": "node scripts/build-electron.js", "electron:pack": "electron-builder --dir", "electron:dist": "electron-builder", "build:frontend": "node scripts/build-frontend.js", "build:all": "npm run build:frontend && npm run electron:build", "test:electron": "node scripts/test-electron.js", "db:setup": "node scripts/setup-database.js", "db:start": "docker compose --file .base/docker-compose.yml up -d", "db:stop": "docker compose --file .base/docker-compose.yml down", "db:reset": "bun run base/cmd.ts db reset"}, "devDependencies": {"@electron/packager": "^18.3.6", "@types/bun": "^1.2.14", "@types/electron": "^1.6.12", "@types/glob": "^8.1.0", "electron": "^36.3.2", "electron-builder": "^26.0.12"}}